# Automatic Test Directory System

This document describes the automatic test directory management system implemented for the TUS-REC2025 Challenge baseline training scripts.

## Overview

The system automatically creates separate test directories for each training run, ensuring organized results storage and comprehensive metrics tracking without manual directory management.

## Key Features

### 🔄 Automatic Test Numbering
- Automatically finds the next available test number
- Separate numbering for different training scripts
- No conflicts or overwrites

### 📊 Comprehensive Metrics Tracking
- Detailed loss breakdown for each epoch
- Complete training history saved as JSON
- Human-readable summaries and reports

### ⚙️ Configuration Management
- Full training configuration saved for each test
- Easy reproduction of training runs
- Version control friendly

### 🏷️ Script Separation
- `train.py` creates `train_test_X` directories
- `train_1.py` creates `train_1_test_X` directories
- Clear distinction between different training approaches

## Directory Structure

```
results/
├── train_test_1/              # Basic MSE training
│   ├── saved_model/           # Model checkpoints
│   ├── train_results/         # Training outputs
│   ├── val_results/           # Validation outputs
│   ├── metrics/               # Detailed metrics
│   │   ├── metrics_epoch_*.json
│   │   ├── complete_metrics_history.json
│   │   └── training_summary.txt
│   ├── logs/                  # Training logs
│   ├── test_config.json       # Machine-readable config
│   ├── test_config.txt        # Human-readable config
│   └── FINAL_TRAINING_SUMMARY.txt
├── train_test_2/
├── train_1_test_1/            # Enhanced loss training
├── train_1_test_2/
└── ...
```

## Usage

### Running Training Scripts

#### Original Training (Basic MSE)
```bash
python train.py [arguments]
```
- Creates: `results/train_test_X/`
- Uses: Standard MSE loss
- Tracks: Basic loss and distance metrics

#### Enhanced Training (Combined Losses)
```bash
python train_1.py [arguments]
```
- Creates: `results/train_1_test_X/`
- Uses: MSE + Correlation + Motion Speed losses
- Tracks: Detailed loss breakdown

### Viewing Results

#### Check Test Summary
```bash
cat results/train_1_test_3/FINAL_TRAINING_SUMMARY.txt
```

#### View Metrics History
```bash
cat results/train_1_test_3/metrics/complete_metrics_history.json
```

#### Compare Configurations
```bash
diff results/train_test_1/test_config.txt results/train_1_test_1/test_config.txt
```

## Generated Files

### Configuration Files

#### `test_config.json`
Machine-readable configuration including:
- Model parameters
- Training settings
- Loss configuration
- Data paths

#### `test_config.txt`
Human-readable configuration summary

### Metrics Files

#### `metrics/metrics_epoch_XXXXXXXX.json`
Per-epoch metrics including:
- Training and validation losses
- Individual loss components
- Distance metrics
- Best results so far

#### `metrics/complete_metrics_history.json`
Complete training history with:
- All epoch results
- Training configuration
- Progress tracking

#### `metrics/training_summary.txt`
Current training status summary

### Final Results

#### `FINAL_TRAINING_SUMMARY.txt`
Complete training summary including:
- Final results
- Best achieved metrics
- Training duration
- Configuration summary

## Implementation Details

### TestManager Class

The `utils/test_manager.py` module provides the `TestManager` class that handles:

```python
from utils.test_manager import TestManager

# Initialize for specific script
test_manager = TestManager('train_1')

# Get next test number
test_number = test_manager.get_next_test_number()

# Create test directory
test_path = test_manager.create_test_directory(test_number, opt, loss_config)

# Track metrics throughout training
test_manager.save_epoch_metrics(...)
test_manager.save_training_summary(...)
test_manager.save_final_summary(...)
```

### Integration Points

Both training scripts are modified to:
1. Initialize TestManager at startup
2. Create automatic test directories
3. Track metrics throughout training
4. Save comprehensive final summaries

## Benefits

### For Researchers
- **No Manual Directory Management**: Automatic test numbering
- **Complete Reproducibility**: Full configuration saved
- **Easy Comparison**: Structured results for different approaches
- **Progress Tracking**: Detailed metrics throughout training

### For Development
- **Version Control Friendly**: Consistent directory structure
- **Automated Backup**: Model checkpoints and results saved
- **Error Recovery**: Complete state saved at each validation
- **Performance Analysis**: Detailed loss breakdown available

## Monitoring Training

### Real-time Monitoring
Training progress is displayed with detailed loss breakdown:
```
Epoch 010 [Train] Total: 0.123456, MSE: 0.100000, Corr: 0.020000, Speed: 0.003456, Dist: 2.345678
Epoch 010 [Val] Total: 0.098765, MSE: 0.080000, Corr: 0.015000, Speed: 0.003765, Dist: 2.123456
```

### Post-training Analysis
Use the generated JSON files for:
- Plotting training curves
- Comparing different loss components
- Analyzing convergence behavior
- Identifying best performing epochs

## Troubleshooting

### Common Issues

#### Permission Errors
Ensure write permissions for the `results/` directory:
```bash
chmod -R 755 results/
```

#### Disk Space
Monitor disk usage as each test saves:
- Model checkpoints
- Detailed metrics
- Training logs

#### JSON Parsing Errors
If metrics files are corrupted:
1. Check the most recent valid epoch file
2. Resume from that point if needed

### Recovery

If training is interrupted:
1. Check the last saved epoch in `metrics/`
2. Use the saved configuration to resume
3. Model checkpoints are available in `saved_model/`

## Future Enhancements

Potential improvements:
- Web dashboard for monitoring multiple tests
- Automatic comparison reports
- Integration with experiment tracking tools
- Automated hyperparameter optimization
- Resource usage tracking

## Demo

Run the demonstration script to see the system in action:
```bash
python demo_test_system.py
```

This shows:
- Current test directories
- Next test numbers
- File structure examples
- Usage examples
