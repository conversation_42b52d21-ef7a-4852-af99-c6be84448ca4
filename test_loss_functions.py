#!/usr/bin/env python3
"""
Test script for the new loss functions to verify they work correctly
"""

import torch
import numpy as np
import sys
import os

# Add the current directory to the path so we can import utils
sys.path.append(os.getcwd())

try:
    from utils.loss_functions import (
        motion_speed_loss, 
        get_correlation_loss_6dof, 
        dof_MSE_loss,
        extract_6dof_from_transforms
    )
    print("✓ Successfully imported loss functions")
except ImportError as e:
    print(f"✗ Import error: {e}")
    print("Note: This is expected if pytorch3d is not installed")
    print("The functions will work when pytorch3d is available")
    sys.exit(0)

def test_extract_6dof():
    """Test 6 DOF extraction from transformation matrices"""
    print("\n=== Testing 6 DOF extraction ===")
    
    # Create a simple transformation matrix
    batch_size = 2
    transforms = torch.eye(4).unsqueeze(0).repeat(batch_size, 1, 1)
    
    # Add some translation
    transforms[0, :3, 3] = torch.tensor([1.0, 2.0, 3.0])
    transforms[1, :3, 3] = torch.tensor([0.5, 1.5, 2.5])
    
    try:
        dof_params = extract_6dof_from_transforms(transforms)
        print(f"✓ 6 DOF extraction successful. Shape: {dof_params.shape}")
        print(f"  Sample output: {dof_params[0]}")
        return True
    except Exception as e:
        print(f"✗ 6 DOF extraction failed: {e}")
        return False

def test_dof_mse_loss():
    """Test 6 DOF MSE loss"""
    print("\n=== Testing 6 DOF MSE Loss ===")
    
    # Create sample 6 DOF parameters
    batch_size = 4
    labels = torch.randn(batch_size, 6)
    outputs = labels + 0.1 * torch.randn(batch_size, 6)  # Add some noise
    
    criterion = torch.nn.MSELoss()
    
    try:
        loss = dof_MSE_loss(labels, outputs, criterion)
        print(f"✓ 6 DOF MSE loss successful. Loss: {loss.item():.6f}")
        return True
    except Exception as e:
        print(f"✗ 6 DOF MSE loss failed: {e}")
        return False

def test_correlation_loss():
    """Test correlation loss for 6 DOF"""
    print("\n=== Testing Correlation Loss ===")
    
    # Create sample 6 DOF parameters with some correlation
    batch_size = 10
    labels = torch.randn(batch_size, 6)
    outputs = 0.8 * labels + 0.2 * torch.randn(batch_size, 6)  # Correlated with noise
    
    try:
        loss = get_correlation_loss_6dof(labels, outputs, dof_based=True)
        print(f"✓ Correlation loss successful. Loss: {loss.item():.6f}")
        return True
    except Exception as e:
        print(f"✗ Correlation loss failed: {e}")
        return False

def test_motion_speed_loss():
    """Test motion speed loss"""
    print("\n=== Testing Motion Speed Loss ===")
    
    # Create sample transformation sequences
    batch_size = 2
    num_frames = 5
    
    # Create identity matrices with some temporal variation
    pred_transforms = torch.eye(4).unsqueeze(0).unsqueeze(0).repeat(batch_size, num_frames, 1, 1)
    gt_transforms = torch.eye(4).unsqueeze(0).unsqueeze(0).repeat(batch_size, num_frames, 1, 1)
    
    # Add some temporal motion
    for t in range(num_frames):
        pred_transforms[:, t, :3, 3] = torch.tensor([t * 0.1, t * 0.2, t * 0.1])
        gt_transforms[:, t, :3, 3] = torch.tensor([t * 0.12, t * 0.18, t * 0.11])  # Slightly different
    
    try:
        loss = motion_speed_loss(pred_transforms, gt_transforms)
        print(f"✓ Motion speed loss successful. Loss: {loss.item():.6f}")
        return True
    except Exception as e:
        print(f"✗ Motion speed loss failed: {e}")
        return False

def main():
    """Run all tests"""
    print("Testing new loss functions for TUS-REC2025 Challenge")
    print("=" * 50)
    
    tests = [
        test_extract_6dof,
        test_dof_mse_loss,
        test_correlation_loss,
        test_motion_speed_loss
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print(f"\n{'='*50}")
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("✓ All tests passed! The loss functions are ready to use.")
    else:
        print("✗ Some tests failed. Check the error messages above.")
        print("Note: Failures might be due to missing dependencies (pytorch3d)")

if __name__ == "__main__":
    main()
