#!/usr/bin/env python3
"""
Test script to demonstrate the improved 3D plotting functionality
"""

import numpy as np
import torch
import os
import sys

# Add utils to path
sys.path.append('utils')

from utils.training_plots import plot_training_metrics_3d, create_training_plots_from_results
from utils.plot_scans import plot_scan_individual, plot_scan_label_pred


def test_training_plots():
    """Test the enhanced training plot functionality"""
    print("Testing enhanced 3D training plots...")
    
    # Create a test directory
    test_dir = "test_plots"
    if not os.path.exists(test_dir):
        os.makedirs(test_dir)
    
    # Test with sample data
    plot_training_metrics_3d(test_dir, create_sample=True)
    print(f"Sample 3D training plots created in {test_dir}/")


def test_scan_plots():
    """Test the improved 3D scan plotting functionality"""
    print("Testing improved 3D scan plots...")
    
    # Create sample scan data
    num_frames = 20
    num_corners = 4
    
    # Generate sample trajectory data (simulating ultrasound probe movement)
    t = np.linspace(0, 2*np.pi, num_frames)
    
    # Create a spiral trajectory for demonstration
    radius = 50
    height_variation = 20
    
    # Ground truth trajectory
    gt_data = np.zeros((num_frames, 3, num_corners))
    
    for frame in range(num_frames):
        # Base position (center of the "ultrasound frame")
        center_x = radius * np.cos(t[frame])
        center_y = radius * np.sin(t[frame])
        center_z = height_variation * np.sin(2 * t[frame])
        
        # Four corners of the ultrasound frame (rectangular)
        frame_width, frame_height = 30, 20
        corners_x = np.array([-frame_width/2, frame_width/2, frame_width/2, -frame_width/2])
        corners_y = np.array([-frame_height/2, -frame_height/2, frame_height/2, frame_height/2])
        corners_z = np.zeros(4)
        
        # Rotate corners based on trajectory orientation
        angle = t[frame]
        cos_a, sin_a = np.cos(angle), np.sin(angle)
        
        # Apply rotation and translation
        gt_data[frame, 0, :] = center_x + corners_x * cos_a - corners_y * sin_a
        gt_data[frame, 1, :] = center_y + corners_x * sin_a + corners_y * cos_a  
        gt_data[frame, 2, :] = center_z + corners_z
    
    # Create prediction data (with some noise/error)
    pred_data = gt_data + np.random.normal(0, 2, gt_data.shape)
    
    # Create sample frame data
    frame_data = torch.rand(num_frames, 64, 64)  # Sample ultrasound frames
    
    # Test directory
    test_dir = "test_plots"
    if not os.path.exists(test_dir):
        os.makedirs(test_dir)
    
    # Test individual plot
    print("Creating individual 3D scan plot...")
    plot_scan_individual(
        gt_data, frame_data, 
        os.path.join(test_dir, "test_scan_individual"),
        step=1, color='green', width=3, scatter=12, legend_size=40, legend='Ground Truth'
    )
    
    # Test comparison plot
    print("Creating comparison 3D scan plot...")
    colors = ['green', 'red']
    plot_scan_label_pred(
        gt_data, pred_data, frame_data, colors,
        os.path.join(test_dir, "test_scan_comparison"),
        step=1, width=3, scatter=12, legend_size=40
    )
    
    print(f"3D scan plots created in {test_dir}/")


def test_existing_results():
    """Test plotting with existing results if available"""
    results_path = "results/seq_len2__efficientnet_b1__lr0.0001__pred_type_parameter__label_type_point"
    
    if os.path.exists(results_path):
        print(f"Testing with existing results from {results_path}...")
        try:
            create_training_plots_from_results(results_path)
            print("Successfully created plots from existing results!")
        except Exception as e:
            print(f"Error creating plots from existing results: {e}")
    else:
        print("No existing results found, skipping this test.")


def main():
    """Main test function"""
    print("=" * 60)
    print("Testing Enhanced 3D Plotting Functionality")
    print("=" * 60)
    
    try:
        # Test training plots
        test_training_plots()
        print()
        
        # Test scan plots  
        test_scan_plots()
        print()
        
        # Test with existing results
        test_existing_results()
        print()
        
        print("=" * 60)
        print("All tests completed successfully!")
        print("Check the 'test_plots' directory for generated plots.")
        print("=" * 60)
        
    except Exception as e:
        print(f"Error during testing: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
