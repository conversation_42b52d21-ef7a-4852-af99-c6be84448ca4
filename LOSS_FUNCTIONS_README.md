# Enhanced Loss Functions for TUS-REC2025 Challenge

This document describes the enhanced loss functions added to improve the training performance of the TUS-REC2025 Challenge baseline model.

## Overview

Based on the DCL-Net methodology and the equations provided, three loss functions have been implemented and integrated into the training pipeline:

1. **6 DOF MSE Loss** (L_mse) - Modified MSE loss working on 6 degrees of freedom
2. **Correlation Loss** (L_corr) - Measures correlation between predicted and ground truth parameters
3. **Motion Speed Loss** (L_speed) - Ensures temporal consistency in motion predictions

The combined loss function is:
```
L = α₁ × L_mse + α₂ × L_corr + α₃ × L_speed
```

## Implementation Details

### 1. Files Modified

#### `utils/loss_functions.py`
- Added `extract_6dof_from_transforms()` - Extracts 6 DOF parameters (tx, ty, tz, rx, ry, rz) from 4x4 transformation matrices
- Added `motion_speed_loss()` - Implements equation (11) for velocity consistency
- Added `get_correlation_loss_6dof()` - Correlation loss for 6 DOF parameters
- Added `dof_MSE_loss()` - MSE loss specifically for 6 DOF parameters

#### `train_1.py`
- Integrated all three loss functions into the training loop
- Added loss weights: α₁=1.0, α₂=0.5, α₃=0.3
- Enhanced logging to track individual loss components
- Updated validation loop to use combined loss

### 2. Key Functions

#### `extract_6dof_from_transforms(transforms)`
Converts 4x4 transformation matrices to 6 DOF parameters:
- **Input**: Tensor of shape (batch, 4, 4) or (batch, num_frames, 4, 4)
- **Output**: Tensor of shape (batch, 6) or (batch, num_frames, 6)
- **Parameters**: [tx, ty, tz, rx, ry, rz] where rotations are Euler angles in ZYX order

#### `motion_speed_loss(pred_transforms, gt_transforms)`
Implements equation (11) from the paper:
```
L_speed = (1/6n-2) × Σᵢ₌₁ⁿ⁻² Σⱼ₌₁⁶ (vᵢⱼ - v̂ᵢⱼ)²
```
Where vᵢⱼ represents the velocity (difference between consecutive frames) for DOF j at frame i.

#### `get_correlation_loss_6dof(labels, outputs, dof_based=True)`
Calculates correlation loss for each DOF separately:
- Measures how well predicted and ground truth parameters correlate
- Returns 1 - correlation_coefficient as loss
- Handles both 2D and 3D tensor inputs

#### `dof_MSE_loss(labels, outputs, criterion)`
MSE loss specifically designed for 6 DOF parameters:
- Automatically extracts 6 DOF if transformation matrices are provided
- Ensures consistent loss calculation across different input formats

### 3. Training Integration

The training loop now calculates and combines three losses:

```python
# Calculate individual losses
mse_loss = dof_MSE_loss(labels, preds, criterion)
corr_loss = get_correlation_loss_6dof(labels, preds, dof_based=True)
speed_loss = motion_speed_loss(preds, labels)  # Only for multi-frame sequences

# Combined loss
loss = alpha1 * mse_loss + alpha2 * corr_loss + alpha3 * speed_loss
```

### 4. Loss Weights

Default weights are set based on typical DCL-Net configurations:
- **α₁ = 1.0** - MSE loss (primary reconstruction loss)
- **α₂ = 0.5** - Correlation loss (feature consistency)
- **α₃ = 0.3** - Motion speed loss (temporal consistency)

These can be adjusted based on validation performance.

## Usage

### Running the Enhanced Training

```bash
python train_1.py [your_existing_arguments]
```

The training will automatically use the combined loss function and display detailed loss breakdown:

```
Epoch 001 [Train] Total: 0.123456, MSE: 0.100000, Corr: 0.020000, Speed: 0.003456, Dist: 2.345678
Epoch 001 [Val] Total: 0.098765, MSE: 0.080000, Corr: 0.015000, Speed: 0.003765, Dist: 2.123456
```

### Testing the Loss Functions

Run the test script to verify all functions work correctly:

```bash
python test_loss_functions.py
```

## Dependencies

The enhanced loss functions require:
- PyTorch
- pytorch3d (for Euler angle conversions)

If pytorch3d is not available, the functions will gracefully handle the error, but 6 DOF extraction will not work.

## Expected Improvements

The enhanced loss functions should provide:

1. **Better 6 DOF Parameter Learning**: Direct optimization on the 6 degrees of freedom
2. **Improved Feature Correlation**: Ensures predicted parameters correlate well with ground truth
3. **Temporal Consistency**: Motion speed loss ensures smooth temporal transitions
4. **More Stable Training**: Combined loss provides multiple optimization signals

## Monitoring Training

The enhanced logging provides detailed breakdown of each loss component, allowing you to:
- Monitor which loss component is dominating
- Adjust loss weights if needed
- Identify convergence issues in specific loss terms

## Troubleshooting

If you encounter issues:

1. **Import Errors**: Ensure pytorch3d is installed: `pip install pytorch3d`
2. **Shape Mismatches**: Check that your transformation matrices are 4x4
3. **NaN Losses**: Reduce learning rate or adjust loss weights
4. **Memory Issues**: Reduce batch size if motion speed loss causes OOM

## Future Enhancements

Potential improvements:
- Adaptive loss weights based on training progress
- Additional regularization terms
- Multi-scale temporal consistency
- Uncertainty-aware loss weighting
