#!/usr/bin/env python3
"""
Test script to demonstrate automatic test directory creation
"""

import os
import glob
import json
import datetime

def get_next_test_number():
    """
    Automatically find the next available test number by checking existing test directories
    """
    test_dirs = glob.glob(os.path.join(os.getcwd(), 'results', 'test_*'))
    if not test_dirs:
        return 1
    
    # Extract test numbers from directory names
    test_numbers = []
    for test_dir in test_dirs:
        dir_name = os.path.basename(test_dir)
        if dir_name.startswith('test_'):
            try:
                test_num = int(dir_name.split('_')[1])
                test_numbers.append(test_num)
            except (ValueError, IndexError):
                continue
    
    return max(test_numbers) + 1 if test_numbers else 1

def create_test_directory_demo(test_number):
    """
    Create a demo test directory to show the structure
    """
    test_dir_name = f"test_{test_number}"
    test_path = os.path.join(os.getcwd(), 'results', test_dir_name)
    
    # Create main test directory
    os.makedirs(test_path, exist_ok=True)
    
    # Create subdirectories
    subdirs = ['saved_model', 'train_results', 'val_results', 'metrics', 'logs']
    for subdir in subdirs:
        os.makedirs(os.path.join(test_path, subdir), exist_ok=True)
    
    # Save demo configuration
    config_data = {
        'test_number': test_number,
        'timestamp': datetime.datetime.now().isoformat(),
        'demo': True,
        'model_config': {
            'model_name': 'efficientnet_b1',
            'num_samples': 2,
            'sample_range': 2,
            'num_pred': 1,
            'pred_type': 'parameter',
            'label_type': 'point',
        },
        'training_config': {
            'batch_size': 16,
            'learning_rate': 0.0001,
            'num_epochs': 100000000,
            'retrain': False,
            'retrain_epoch': '00000000',
        },
        'loss_config': {
            'alpha1_mse': 1.0,
            'alpha2_corr': 0.5,
            'alpha3_speed': 0.3,
            'loss_description': 'Combined loss: L = α₁L_mse + α₂L_corr + α₃L_speed'
        }
    }
    
    # Save configuration as JSON
    config_file = os.path.join(test_path, 'test_config.json')
    with open(config_file, 'w') as f:
        json.dump(config_data, f, indent=4)
    
    # Save configuration as readable text
    config_txt_file = os.path.join(test_path, 'test_config.txt')
    with open(config_txt_file, 'w') as f:
        f.write(f"=== Test {test_number} Configuration ===\n")
        f.write(f"Created: {config_data['timestamp']}\n")
        f.write("Status: DEMO - Created by test script\n\n")
        
        for section, params in config_data.items():
            if isinstance(params, dict):
                f.write(f"[{section.upper()}]\n")
                for key, value in params.items():
                    f.write(f"  {key}: {value}\n")
                f.write("\n")
    
    # Create demo metrics file
    demo_metrics = {
        'training_info': {
            'test_number': test_number,
            'start_time': datetime.datetime.now().isoformat(),
            'demo': True
        },
        'metrics_history': {
            'train': {
                'epochs': [1, 2, 3],
                'total_loss': [0.5, 0.4, 0.3],
                'mse_loss': [0.3, 0.25, 0.2],
                'corr_loss': [0.15, 0.12, 0.08],
                'speed_loss': [0.05, 0.03, 0.02],
                'distance': [2.5, 2.1, 1.8]
            },
            'val': {
                'epochs': [1, 2, 3],
                'total_loss': [0.45, 0.38, 0.32],
                'mse_loss': [0.28, 0.23, 0.21],
                'corr_loss': [0.13, 0.11, 0.09],
                'speed_loss': [0.04, 0.04, 0.02],
                'distance': [2.3, 2.0, 1.9]
            }
        }
    }
    
    demo_metrics_file = os.path.join(test_path, 'metrics', 'demo_metrics.json')
    with open(demo_metrics_file, 'w') as f:
        json.dump(demo_metrics, f, indent=4)
    
    # Create demo summary
    summary_file = os.path.join(test_path, 'DEMO_SUMMARY.txt')
    with open(summary_file, 'w') as f:
        f.write(f"=== DEMO TEST {test_number} ===\n")
        f.write(f"Created: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        f.write("This is a demonstration of the automatic test directory structure.\n")
        f.write("When you run train_1.py, it will automatically:\n")
        f.write("1. Find the next available test number\n")
        f.write("2. Create a new test directory with that number\n")
        f.write("3. Save all training configurations and metrics\n")
        f.write("4. Track progress throughout training\n\n")
        f.write("Directory structure:\n")
        f.write("- saved_model/     : Model checkpoints\n")
        f.write("- train_results/   : Training outputs\n")
        f.write("- val_results/     : Validation outputs\n")
        f.write("- metrics/         : Detailed metrics and history\n")
        f.write("- logs/            : Training logs\n")
        f.write("- test_config.json : Complete configuration\n")
        f.write("- FINAL_TRAINING_SUMMARY.txt : Final results\n")
    
    return test_path

def main():
    """
    Demonstrate the automatic test directory creation
    """
    print("🧪 Testing Automatic Test Directory Creation")
    print("=" * 50)
    
    # Check current test directories
    existing_tests = glob.glob(os.path.join(os.getcwd(), 'results', 'test_*'))
    print(f"📁 Existing test directories: {len(existing_tests)}")
    for test_dir in sorted(existing_tests):
        print(f"   - {os.path.basename(test_dir)}")
    
    # Get next test number
    next_test = get_next_test_number()
    print(f"\n🔢 Next test number will be: {next_test}")
    
    # Create demo directory
    test_path = create_test_directory_demo(next_test)
    print(f"✅ Created demo test directory: {test_path}")
    
    # Show directory structure
    print(f"\n📂 Directory structure created:")
    for root, dirs, files in os.walk(test_path):
        level = root.replace(test_path, '').count(os.sep)
        indent = ' ' * 2 * level
        print(f"{indent}{os.path.basename(root)}/")
        subindent = ' ' * 2 * (level + 1)
        for file in files:
            print(f"{subindent}{file}")
    
    print(f"\n🎯 When you run train_1.py next time, it will automatically create test_{next_test + 1}")
    print(f"📋 Check {test_path} to see the demo structure")

if __name__ == "__main__":
    main()
