# Training script

import os
import torch
import json
import datetime
from torch.utils.tensorboard import SummaryWriter
from utils.loader import Dataset
from utils.network import build_model
from utils.loss import PointDistance
from utils.test_manager import TestManager
from utils.plot_functions import *
from utils.transform import LabelTransform, PredictionTransform, PointTransform
from options.train_options import TrainOptions
from utils.funs import *


# Parse options and setup test management
opt = TrainOptions().parse()

# Initialize test manager for original train script
test_manager = TestManager('train')

# Loss configuration for original train script (basic MSE)
loss_config = {
    'loss_type': 'MSE',
    'loss_description': 'Standard MSE loss for baseline training'
}

# Get next test number and create test directory
test_number = test_manager.get_next_test_number()
test_path = test_manager.create_test_directory(test_number, opt, loss_config)

# Override SAVE_PATH to use the new test directory
opt.SAVE_PATH = os.path.relpath(test_path, os.getcwd())

# Print test header
test_manager.print_test_header(test_number, test_path)

writer = SummaryWriter(os.path.join(os.getcwd(),opt.SAVE_PATH))
os.environ["CUDA_VISIBLE_DEVICES"] = opt.gpu_ids
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
# get data pairs for prediction
data_pairs = pair_samples(opt.NUM_SAMPLES, opt.NUM_PRED, 0).to(device)
with open(os.getcwd()+'/'+ opt.SAVE_PATH +'/'+ 'data_pairs.json', 'w', encoding='utf-8') as fp:
    json.dump(data_pairs.cpu().numpy().tolist(), fp, ensure_ascii=False, indent=4)

# all available data
dataset_all = Dataset(
    data_path=opt.DATA_PATH,
    num_samples=opt.NUM_SAMPLES,
    sample_range=opt.SAMPLE_RANGE
    )

## split entire dataset into Train, Val, and Test
dset_folds = dataset_all.partition_by_ratio(
    ratios = [1]*5,
    randomise=True,
    )
# save the indices of the splited train, val and test dataset, for reproducibility
for (idx, ds) in enumerate(dset_folds):
    ds.write_json(os.path.join(os.getcwd(),opt.SAVE_PATH,"fold_{:02d}.json".format(idx)))

# train, val and test dataset
dset_train = dset_folds[0]+dset_folds[1]+dset_folds[2]
dset_val = dset_folds[3]
# dset_test = dset_folds[4]

# data loader
train_loader = torch.utils.data.DataLoader(
    dset_train,
    batch_size=opt.MINIBATCH_SIZE,
    shuffle=True,
    num_workers=8,
    pin_memory=True
    )

val_loader = torch.utils.data.DataLoader(
    dset_val,
    batch_size=1,
    shuffle=False,
    num_workers=8,
    pin_memory=True
    )


## load calibration metric
tform_calib_scale,tform_calib_R_T, tform_calib = read_calib_matrices(os.path.join(os.getcwd(),opt.FILENAME_CALIB))
# Coordinates of four corner points in image coordinate system (in pixel)
image_points = reference_image_points(dset_train[0][0].shape[1:],2).to(device)
# dimension for prediction and label
pred_dim = type_dim(opt.PRED_TYPE, image_points.shape[1], data_pairs.shape[0])
label_dim = type_dim(opt.LABEL_TYPE, image_points.shape[1], data_pairs.shape[0])
# transform label into another type, e.g., transform 4*4 transformation matrix into points
transform_label = LabelTransform(
    opt.LABEL_TYPE,
    pairs=data_pairs,
    image_points=image_points,
    tform_image_to_tool=tform_calib.to(device),
    tform_image_mm_to_tool=tform_calib_R_T.to(device),
    tform_image_pixel_to_mm = tform_calib_scale.to(device)
    )
# transform prediction into the type of label
transform_prediction = PredictionTransform(
    opt.PRED_TYPE,
    opt.LABEL_TYPE,
    num_pairs=data_pairs.shape[0],
    image_points=image_points,
    tform_image_to_tool=tform_calib.to(device),
    tform_image_mm_to_tool=tform_calib_R_T.to(device),
    tform_image_pixel_to_mm = tform_calib_scale.to(device)
    )
# transform into points, from the type of label
transform_into_points = PointTransform(
    label_type=opt.LABEL_TYPE,
    image_points=image_points,
    tform_image_to_tool=tform_calib.to(device),
    tform_image_mm_to_tool=tform_calib_R_T.to(device),
    tform_image_pixel_to_mm = tform_calib_scale.to(device)
    )

# network
model = build_model(
    opt,
    in_frames = opt.NUM_SAMPLES,
    pred_dim = pred_dim,
    ).to(device)

if opt.retrain:
    # retrain model from previous epoch
    model.load_state_dict(torch.load(os.path.join(os.getcwd(),opt.SAVE_PATH,'saved_model', 'model_epoch'+str(opt.retrain_epoch)),map_location=torch.device(device)))
else:
    # load model weights trained using data of TUS-REC2024
    model.load_state_dict(torch.load(os.path.join(os.getcwd(),'TUS-REC2024_model', 'model_weights'),map_location=torch.device(device)))


## training
val_loss_min = 1e10
val_dist_min = 1e10
optimiser = torch.optim.Adam(model.parameters(), lr=opt.LEARNING_RATE)
criterion = torch.nn.MSELoss()
metrics = PointDistance()

# Initialize metrics tracking using test manager
metrics_history = test_manager.initialize_metrics_tracking()

# Save initial training info
training_info = {
    'test_number': test_number,
    'start_time': datetime.datetime.now().isoformat(),
    'device': str(device),
    'model_parameters': sum(p.numel() for p in model.parameters()),
    'trainable_parameters': sum(p.numel() for p in model.parameters() if p.requires_grad)
}

print('Training started with standard MSE loss')
print(f'Model parameters: {training_info["model_parameters"]:,}')
print(f'Trainable parameters: {training_info["trainable_parameters"]:,}')
print(f'Device: {device}')

for epoch in range(int(opt.retrain_epoch), int(opt.retrain_epoch)+opt.NUM_EPOCHS):

    train_epoch_loss = 0
    train_epoch_dist = 0
    for step, (frames, tforms,_,_) in enumerate(train_loader):
        frames, tforms = frames.to(device), tforms.to(device)
        tforms_inv = torch.linalg.inv(tforms)
        frames = frames/255
        # transform label based on label type
        labels = transform_label(tforms, tforms_inv)

        optimiser.zero_grad()
        # model prediction
        outputs = model(frames)
        # transform prediction according to label type
        preds = transform_prediction(outputs)
        # calculate loss
        loss = criterion(preds, labels)
        loss.backward()
        optimiser.step()

        # transfrom prediction and label into points, for metric calculation
        preds_pts = transform_into_points(preds.data)
        labels_pts = transform_into_points(labels)
        dist = metrics(preds_pts, labels_pts).detach()

        train_epoch_loss += loss.item()
        train_epoch_dist += dist

    train_epoch_loss /= (step + 1)
    train_epoch_dist /= (step + 1)

    # Save training metrics
    metrics_history['train']['epochs'].append(epoch)
    metrics_history['train']['total_loss'].append(train_epoch_loss)
    metrics_history['train']['mse_loss'].append(train_epoch_loss)  # Same as total for basic MSE
    metrics_history['train']['corr_loss'].append(0.0)  # Not used in basic training
    metrics_history['train']['speed_loss'].append(0.0)  # Not used in basic training
    metrics_history['train']['distance'].append(float(train_epoch_dist))

    # print loss information on terminal
    print_info(epoch,train_epoch_loss,train_epoch_dist,opt,'train')

    # validation
    if epoch in range(int(opt.retrain_epoch), int(opt.retrain_epoch)+opt.NUM_EPOCHS, opt.val_fre):

        model.train(False)
        epoch_loss_val = 0
        epoch_dist_val = 0
        with torch.no_grad():
            for step, (fr_val, tf_val,_,_) in enumerate(val_loader):

                fr_val, tf_val = fr_val.to(device), tf_val.to(device)
                tf_val_inv = torch.linalg.inv(tf_val)
                # transform label based on label type
                la_val = transform_label(tf_val, tf_val_inv)
                fr_val = fr_val/255

                out_val = model(fr_val)
                # transform prediction
                pr_val = transform_prediction(out_val)
                # calculate loss and metric
                loss_val = criterion(pr_val, la_val)
                pr_val_pts = transform_into_points(pr_val)
                la_val_pts = transform_into_points(la_val)
                dist_val = metrics(pr_val_pts, la_val_pts).detach()

                epoch_loss_val += loss_val.item()
                epoch_dist_val += dist_val


            epoch_loss_val /= (step+1)
            epoch_dist_val /= (step+1)

        # Save validation metrics
        metrics_history['val']['epochs'].append(epoch)
        metrics_history['val']['total_loss'].append(epoch_loss_val)
        metrics_history['val']['mse_loss'].append(epoch_loss_val)  # Same as total for basic MSE
        metrics_history['val']['corr_loss'].append(0.0)  # Not used in basic training
        metrics_history['val']['speed_loss'].append(0.0)  # Not used in basic training
        metrics_history['val']['distance'].append(float(epoch_dist_val))

        # print loss information on terminal
        print_info(epoch,epoch_loss_val,epoch_dist_val,opt,'val')
        # save model at current epoch
        save_model(model,epoch,opt)
        # save best validation model
        val_loss_min, val_dist_min = save_best_network(opt, model, epoch, epoch_loss_val, epoch_dist_val.mean(), val_loss_min, val_dist_min)

        # add to tensorboard and save to txt
        loss_dists = {'train_epoch_loss': train_epoch_loss, 'train_epoch_dist': train_epoch_dist,'val_epoch_loss':epoch_loss_val,'val_epoch_dist':epoch_dist_val}
        add_scalars(writer, epoch, loss_dists)
        write_to_txt(opt, epoch, loss_dists)

        # Use test manager to save comprehensive metrics
        train_metrics = {
            'total_loss': train_epoch_loss,
            'mse_loss': train_epoch_loss,
            'corr_loss': 0.0,
            'speed_loss': 0.0,
            'distance': float(train_epoch_dist)
        }

        val_metrics = {
            'total_loss': epoch_loss_val,
            'mse_loss': epoch_loss_val,
            'corr_loss': 0.0,
            'speed_loss': 0.0,
            'distance': float(epoch_dist_val)
        }

        best_metrics = {
            'val_loss_min': val_loss_min,
            'val_dist_min': float(val_dist_min)
        }

        # Save epoch metrics using test manager
        test_manager.save_epoch_metrics(
            test_path, epoch, train_metrics, val_metrics,
            best_metrics, metrics_history, training_info
        )

        # Save training summary using test manager
        test_manager.save_training_summary(
            test_path, test_number, metrics_history, training_info,
            epoch, train_metrics, val_metrics, best_metrics
        )

        model.train(True)

# Training completed - save final summary using test manager
final_summary_file = test_manager.save_final_summary(
    test_path, test_number, metrics_history, training_info, loss_config
)

# Print completion summary using test manager
test_manager.print_completion_summary(
    test_number, test_path, metrics_history, final_summary_file
)

print(f"✅ All training data and metrics saved successfully!")
print(f"📋 Check {final_summary_file} for complete training summary")

