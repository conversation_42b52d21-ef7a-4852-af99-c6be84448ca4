#!/usr/bin/env python3
"""
Script to regenerate existing 3D plots with improved visualization
"""

import os
import sys
import glob
import shutil
from pathlib import Path

# Add utils to path
sys.path.append('utils')

from utils.training_plots import create_training_plots_from_results


def regenerate_training_plots():
    """Regenerate training plots for all existing results directories"""
    print("Regenerating enhanced 3D training plots for existing results...")
    
    results_base = "results"
    if not os.path.exists(results_base):
        print(f"No results directory found at {results_base}")
        return
    
    # Find all result directories
    result_dirs = [d for d in os.listdir(results_base) 
                   if os.path.isdir(os.path.join(results_base, d))]
    
    if not result_dirs:
        print("No result directories found")
        return
    
    print(f"Found {len(result_dirs)} result directories:")
    for i, dir_name in enumerate(result_dirs, 1):
        print(f"  {i}. {dir_name}")
    
    # Process each directory
    for dir_name in result_dirs:
        result_path = os.path.join(results_base, dir_name)
        print(f"\nProcessing: {dir_name}")
        
        try:
            create_training_plots_from_results(result_path)
            print(f"✓ Enhanced plots created for {dir_name}")
        except Exception as e:
            print(f"✗ Error processing {dir_name}: {e}")


def backup_existing_plots():
    """Create backup of existing plots before regenerating"""
    print("Creating backup of existing plots...")
    
    results_base = "results"
    backup_base = "plots_backup"
    
    if not os.path.exists(results_base):
        return
    
    if not os.path.exists(backup_base):
        os.makedirs(backup_base)
    
    # Find all PNG files in results directories
    png_files = glob.glob(os.path.join(results_base, "**", "*.png"), recursive=True)
    
    for png_file in png_files:
        # Create relative path for backup
        rel_path = os.path.relpath(png_file, results_base)
        backup_path = os.path.join(backup_base, rel_path)
        
        # Create backup directory if needed
        backup_dir = os.path.dirname(backup_path)
        if not os.path.exists(backup_dir):
            os.makedirs(backup_dir)
        
        # Copy file
        shutil.copy2(png_file, backup_path)
    
    print(f"Backed up {len(png_files)} plot files to {backup_base}/")


def create_comparison_plots():
    """Create comparison between old and new plots"""
    print("Creating comparison documentation...")
    
    comparison_dir = "plot_comparison"
    if not os.path.exists(comparison_dir):
        os.makedirs(comparison_dir)
    
    # Create a simple comparison report
    report_content = """# 3D Plot Enhancement Report

## Improvements Made

### 1. Enhanced 3D Scan Visualization (`utils/plot_scans.py`)

**Key Improvements:**
- **Continuous 3D trajectory lines**: Added proper 3D lines connecting corner points across frames
- **Better frame visualization**: Enhanced first/last frame highlighting with distinct colors
- **Intermediate frame outlines**: Added semi-transparent intermediate frames for better 3D structure
- **Improved viewing angles**: Optimized camera angles for better 3D perception
- **Enhanced axis settings**: Equal aspect ratios and better labeling
- **Better color schemes**: Improved color contrast and alpha blending

**Technical Changes:**
- Added trajectory lines for each corner point across all frames
- Implemented proper 3D line connectivity using `ax.plot(x_traj, y_traj, z_traj)`
- Enhanced frame rectangle visualization with intermediate frames
- Improved axis formatting with proper labels and tick sizes
- Added `set_box_aspect([1,1,1])` for equal 3D proportions

### 2. New Enhanced Training Visualization (`utils/training_plots.py`)

**Features:**
- **3D Loss Surface Plots**: Visualize training/validation loss as 3D surfaces
- **3D Distance Evolution**: Show distance metrics in 3D space
- **Combined Metrics Visualization**: Loss vs Distance relationship in 3D
- **Traditional 2D Comparisons**: Side-by-side with enhanced 2D plots
- **Convergence Analysis**: Training improvement rate visualization
- **Multiple Export Formats**: PNG and PDF outputs

**Technical Features:**
- Reads training data from existing text files
- Creates sample data for demonstration
- Multiple subplot layout with 3D and 2D visualizations
- Enhanced color schemes and transparency
- Professional formatting and labeling

### 3. Integration with Training Pipeline

**Added Functions:**
- `create_enhanced_training_plots()` in `utils/funs.py`
- Automatic plot generation at training completion
- Backward compatibility with existing TensorBoard logging

## Usage

### For Existing Results:
```python
from utils.training_plots import create_training_plots_from_results
create_training_plots_from_results("path/to/results")
```

### For New Training:
The enhanced plots are automatically generated when training completes.

### Manual Testing:
```bash
python test_3d_plots.py
```

## Files Modified:
1. `utils/plot_scans.py` - Enhanced 3D scan plotting
2. `utils/funs.py` - Added training plot integration
3. `utils/training_plots.py` - New comprehensive training visualization
4. `test_3d_plots.py` - Test and demonstration script
5. `regenerate_3d_plots.py` - Utility to regenerate existing plots

## Results:
- Significantly improved 3D visualization quality
- Better understanding of training progression
- Enhanced scientific presentation quality
- Maintained compatibility with existing workflow
"""

    with open(os.path.join(comparison_dir, "enhancement_report.md"), "w") as f:
        f.write(report_content)
    
    print(f"Enhancement report created in {comparison_dir}/enhancement_report.md")


def main():
    """Main function"""
    print("=" * 60)
    print("3D Plot Enhancement and Regeneration Tool")
    print("=" * 60)
    
    try:
        # Create backup of existing plots
        backup_existing_plots()
        print()
        
        # Regenerate training plots with enhancements
        regenerate_training_plots()
        print()
        
        # Create comparison documentation
        create_comparison_plots()
        print()
        
        print("=" * 60)
        print("Plot regeneration completed successfully!")
        print()
        print("Summary:")
        print("- Existing plots backed up to 'plots_backup/'")
        print("- Enhanced 3D training plots generated")
        print("- Comparison report created in 'plot_comparison/'")
        print("- Test plots available in 'test_plots/'")
        print()
        print("The enhanced 3D plotting now provides:")
        print("✓ Proper 3D trajectory lines")
        print("✓ Better frame connectivity visualization")
        print("✓ Enhanced training metrics in 3D")
        print("✓ Professional scientific presentation quality")
        print("=" * 60)
        
    except Exception as e:
        print(f"Error during plot regeneration: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
