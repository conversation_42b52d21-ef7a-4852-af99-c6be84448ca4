# -*- coding: utf-8 -*-
"""
Loss functions designed by myself, for better training the networks
The input should be 2 batch_size x n_dimensional vector: network outputs and labels
"""

# %%

import numpy as np
import torch
try:
    import pytorch3d.transforms
    PYTORCH3D_AVAILABLE = True
except ImportError:
    PYTORCH3D_AVAILABLE = False
    print("Warning: pytorch3d not available. 6 DOF extraction will use fallback method.")


# %%
''' Correlation loss for evaluator '''
def correlation_loss(output, target):
    x = output.flatten()
    y = target
    # print('x shape {}, y shape {}'.format(x.shape, y.shape))
    xy = x * y
    mean_xy = torch.mean(xy)
    mean_x = torch.mean(x)
    mean_y = torch.mean(y)
    cov_xy = mean_xy - mean_x * mean_y
    # print('xy shape {}'.format(xy.shape))
    # print('xy {}'.format(xy))
    # print('mean_xy {}'.format(mean_xy))
    # print('cov_xy {}'.format(cov_xy))

    var_x = torch.sum((x - mean_x) ** 2 / x.shape[0])
    var_y = torch.sum((y - mean_y) ** 2 / y.shape[0])
    # print('var_x {}'.format(var_x))

    corr_xy = cov_xy / (torch.sqrt(var_x * var_y))
    # print('correlation_xy {}'.format(corr_xy))

    loss = 1 - corr_xy
    # time.sleep(30)
    # x = output
    # y = target
    #
    # vx = x - torch.mean(x)
    # vy = y - torch.mean(y)
    #
    # loss = torch.sum(vx * vy) / (torch.sqrt(torch.sum(vx ** 2)) * torch.sqrt(torch.sum(vy ** 2)))
    # print(loss)
    return loss

def correlation_loss_np(output, target):
    # output = output.data.cpu().numpy()
    # target = target.data.cpu().numpy()
    # output = output.flatten()
    print('output {}, target {}'.format(output.shape, target.shape))
    correlation = np.corrcoef(output, target)[0, 1]
    # loss = 1 - correlation

    return correlation

def matrix_to_euler_angles_fallback(rotation_matrices):
    """
    Fallback function to convert rotation matrices to Euler angles when pytorch3d is not available
    Uses ZYX convention (same as pytorch3d default)
    """
    # Extract individual rotation matrix elements
    r11 = rotation_matrices[..., 0, 0]
    r12 = rotation_matrices[..., 0, 1]
    r13 = rotation_matrices[..., 0, 2]
    r21 = rotation_matrices[..., 1, 0]
    r22 = rotation_matrices[..., 1, 1]
    r23 = rotation_matrices[..., 1, 2]
    r31 = rotation_matrices[..., 2, 0]
    r32 = rotation_matrices[..., 2, 1]
    r33 = rotation_matrices[..., 2, 2]

    # Convert to Euler angles (ZYX convention)
    sy = torch.sqrt(r11 * r11 + r21 * r21)

    singular = sy < 1e-6

    x = torch.where(singular,
                   torch.atan2(-r23, r22),
                   torch.atan2(r32, r33))
    y = torch.where(singular,
                   torch.atan2(-r13, sy),
                   torch.atan2(-r13, sy))
    z = torch.where(singular,
                   torch.zeros_like(r11),
                   torch.atan2(r21, r11))

    return torch.stack([z, y, x], dim=-1)  # ZYX order


def extract_6dof_from_transforms(transforms):
    """
    Extract 6 DOF parameters (tx, ty, tz, rx, ry, rz) from 4x4 transformation matrices

    Args:
        transforms: torch.Tensor of shape (batch, num_frames, 4, 4) or (batch, 4, 4)

    Returns:
        torch.Tensor of shape (batch, num_frames, 6) or (batch, 6) containing [tx, ty, tz, rx, ry, rz]
    """
    if transforms.dim() == 3:
        # Single transform per batch item
        batch_size = transforms.shape[0]
        # Extract translation
        translation = transforms[:, :3, 3]  # (batch, 3)
        # Extract rotation matrix and convert to euler angles
        rotation_matrices = transforms[:, :3, :3]  # (batch, 3, 3)

        if PYTORCH3D_AVAILABLE:
            euler_angles = pytorch3d.transforms.matrix_to_euler_angles(rotation_matrices, 'ZYX')  # (batch, 3)
        else:
            euler_angles = matrix_to_euler_angles_fallback(rotation_matrices)  # (batch, 3)

        # Combine translation and rotation
        dof_params = torch.cat([translation, euler_angles], dim=1)  # (batch, 6)
        return dof_params

    elif transforms.dim() == 4:
        # Multiple transforms per batch item
        batch_size, num_frames = transforms.shape[0], transforms.shape[1]
        # Extract translation
        translation = transforms[:, :, :3, 3]  # (batch, num_frames, 3)
        # Extract rotation matrix and convert to euler angles
        rotation_matrices = transforms[:, :, :3, :3]  # (batch, num_frames, 3, 3)

        if PYTORCH3D_AVAILABLE:
            # Reshape for pytorch3d
            rot_reshaped = rotation_matrices.view(-1, 3, 3)
            euler_angles = pytorch3d.transforms.matrix_to_euler_angles(rot_reshaped, 'ZYX')  # (batch*num_frames, 3)
            euler_angles = euler_angles.view(batch_size, num_frames, 3)  # (batch, num_frames, 3)
        else:
            euler_angles = matrix_to_euler_angles_fallback(rotation_matrices)  # (batch, num_frames, 3)

        # Combine translation and rotation
        dof_params = torch.cat([translation, euler_angles], dim=2)  # (batch, num_frames, 6)
        return dof_params

    else:
        raise ValueError(f"Unsupported transform tensor dimensions: {transforms.shape}")


def motion_speed_loss(pred_data, gt_data):
    """
    Calculate motion speed loss (L_speed) as described in equation (11)
    Adapts to different input formats

    Args:
        pred_data: Predicted data (transformations, parameters, or points)
        gt_data: Ground truth data (transformations, parameters, or points)

    Returns:
        torch.Tensor: motion speed loss (0 if temporal dimension not available)
    """
    # Check if we have temporal dimension
    if pred_data.dim() < 3:
        # No temporal dimension, return zero loss
        return torch.tensor(0.0, device=pred_data.device)

    # If inputs are transformation matrices, extract 6 DOF parameters
    if pred_data.dim() >= 3 and pred_data.shape[-1] == 4 and pred_data.shape[-2] == 4:
        pred_6dof = extract_6dof_from_transforms(pred_data)
    else:
        # Assume the data is already in parameter or point format
        pred_6dof = pred_data

    if gt_data.dim() >= 3 and gt_data.shape[-1] == 4 and gt_data.shape[-2] == 4:
        gt_6dof = extract_6dof_from_transforms(gt_data)
    else:
        # Assume the data is already in parameter or point format
        gt_6dof = gt_data

    # Check if we have enough frames for velocity calculation
    if pred_6dof.shape[1] < 2:
        return torch.tensor(0.0, device=pred_data.device)

    # Calculate velocities between consecutive frames
    pred_velocities = pred_6dof[:, 1:] - pred_6dof[:, :-1]    # (batch, num_frames-1, dims)
    gt_velocities = gt_6dof[:, 1:] - gt_6dof[:, :-1]          # (batch, num_frames-1, dims)

    # Calculate L2 norm of velocity differences
    velocity_diff = pred_velocities - gt_velocities           # (batch, num_frames-1, dims)
    speed_loss = torch.mean(torch.sum(velocity_diff ** 2, dim=2))  # Mean over batch and frames

    return speed_loss


def get_correlation_loss_adaptive(labels, outputs, dof_based=True):
    """
    Calculate correlation loss that adapts to the actual dimensions of labels and outputs

    Args:
        labels: Ground truth data (any shape)
        outputs: Predicted data (any shape)
        dof_based: If True and both have same dimensions, calculate correlation for each dimension separately

    Returns:
        torch.Tensor: correlation loss
    """
    # If inputs are transformation matrices, extract 6 DOF parameters
    if labels.dim() >= 3 and labels.shape[-1] == 4 and labels.shape[-2] == 4:
        labels = extract_6dof_from_transforms(labels)
    if outputs.dim() >= 3 and outputs.shape[-1] == 4 and outputs.shape[-2] == 4:
        outputs = extract_6dof_from_transforms(outputs)

    # Check if dimensions match
    if labels.shape != outputs.shape:
        # If dimensions don't match, use overall correlation
        x = outputs.flatten()
        y = labels.flatten()

        xy = x * y
        mean_xy = torch.mean(xy)
        mean_x = torch.mean(x)
        mean_y = torch.mean(y)
        cov_xy = mean_xy - mean_x * mean_y

        var_x = torch.sum((x - mean_x) ** 2 / x.shape[0])
        var_y = torch.sum((y - mean_y) ** 2 / y.shape[0])

        corr_xy = cov_xy / (torch.sqrt(var_x * var_y) + 1e-8)
        loss = 1 - corr_xy
        return loss

    # If dimensions match, proceed with dimension-wise correlation if requested
    if dof_based and labels.dim() >= 2:
        dof_correlations = []
        num_dofs = labels.shape[-1]

        for dof_id in range(num_dofs):
            if labels.dim() == 2:
                x = outputs[:, dof_id]
                y = labels[:, dof_id]
            else:  # 3D case - flatten temporal dimension
                x = outputs[:, :, dof_id].flatten()
                y = labels[:, :, dof_id].flatten()

            # Calculate correlation for this dimension
            xy = x * y
            mean_xy = torch.mean(xy)
            mean_x = torch.mean(x)
            mean_y = torch.mean(y)
            cov_xy = mean_xy - mean_x * mean_y

            var_x = torch.sum((x - mean_x) ** 2 / x.shape[0])
            var_y = torch.sum((y - mean_y) ** 2 / y.shape[0])

            corr_xy = cov_xy / (torch.sqrt(var_x * var_y) + 1e-8)  # Add small epsilon for stability
            loss = 1 - corr_xy
            dof_correlations.append(loss)

        loss = sum(dof_correlations) / num_dofs
    else:
        # Overall correlation
        x = outputs.flatten()
        y = labels.flatten()

        xy = x * y
        mean_xy = torch.mean(xy)
        mean_x = torch.mean(x)
        mean_y = torch.mean(y)
        cov_xy = mean_xy - mean_x * mean_y

        var_x = torch.sum((x - mean_x) ** 2 / x.shape[0])
        var_y = torch.sum((y - mean_y) ** 2 / y.shape[0])

        corr_xy = cov_xy / (torch.sqrt(var_x * var_y) + 1e-8)
        loss = 1 - corr_xy

    return loss


# Keep the old function name for backward compatibility
def get_correlation_loss_6dof(labels, outputs, dof_based=True):
    """
    Backward compatibility wrapper for get_correlation_loss_adaptive
    """
    return get_correlation_loss_adaptive(labels, outputs, dof_based)


def dof_MSE_loss(labels, outputs, criterion):
    """
    Calculate MSE loss that adapts to the actual dimensions of labels and outputs

    Args:
        labels: Ground truth data (any shape)
        outputs: Predicted data (any shape)
        criterion: MSE loss function

    Returns:
        torch.Tensor: MSE loss
    """
    # If inputs are transformation matrices, extract 6 DOF parameters
    if labels.dim() >= 3 and labels.shape[-1] == 4 and labels.shape[-2] == 4:
        labels = extract_6dof_from_transforms(labels)
    if outputs.dim() >= 3 and outputs.shape[-1] == 4 and outputs.shape[-2] == 4:
        outputs = extract_6dof_from_transforms(outputs)

    # If dimensions don't match, we can't compute MSE directly
    # This typically happens when model outputs parameters but labels are points
    # In this case, use the standard MSE loss as-is (the transformation handles the conversion)
    loss = criterion(outputs, labels)
    return loss


if __name__ == '__main__':
    x = np.linspace(1, 50, num=50)
    y = 3 * x
    print(x)
    print(y)
    # loss = correlation_loss(output=x, target=y)
    # print('loss = {:.4f}'.format(loss))
