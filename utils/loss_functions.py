# -*- coding: utf-8 -*-
"""
Loss functions designed by myself, for better training the networks
The input should be 2 batch_size x n_dimensional vector: network outputs and labels
"""

# %%

import cv2
import matplotlib.pyplot as plt
import numpy as np
import torch
import time
import pytorch3d.transforms


# %%
''' Correlation loss for evaluator '''
def correlation_loss(output, target):
    x = output.flatten()
    y = target
    # print('x shape {}, y shape {}'.format(x.shape, y.shape))
    xy = x * y
    mean_xy = torch.mean(xy)
    mean_x = torch.mean(x)
    mean_y = torch.mean(y)
    cov_xy = mean_xy - mean_x * mean_y
    # print('xy shape {}'.format(xy.shape))
    # print('xy {}'.format(xy))
    # print('mean_xy {}'.format(mean_xy))
    # print('cov_xy {}'.format(cov_xy))

    var_x = torch.sum((x - mean_x) ** 2 / x.shape[0])
    var_y = torch.sum((y - mean_y) ** 2 / y.shape[0])
    # print('var_x {}'.format(var_x))

    corr_xy = cov_xy / (torch.sqrt(var_x * var_y))
    # print('correlation_xy {}'.format(corr_xy))

    loss = 1 - corr_xy
    # time.sleep(30)
    # x = output
    # y = target
    #
    # vx = x - torch.mean(x)
    # vy = y - torch.mean(y)
    #
    # loss = torch.sum(vx * vy) / (torch.sqrt(torch.sum(vx ** 2)) * torch.sqrt(torch.sum(vy ** 2)))
    # print(loss)
    return loss

def correlation_loss_np(output, target):
    # output = output.data.cpu().numpy()
    # target = target.data.cpu().numpy()
    # output = output.flatten()
    print('output {}, target {}'.format(output.shape, target.shape))
    correlation = np.corrcoef(output, target)[0, 1]
    # loss = 1 - correlation

    return correlation

def extract_6dof_from_transforms(transforms):
    """
    Extract 6 DOF parameters (tx, ty, tz, rx, ry, rz) from 4x4 transformation matrices

    Args:
        transforms: torch.Tensor of shape (batch, num_frames, 4, 4) or (batch, 4, 4)

    Returns:
        torch.Tensor of shape (batch, num_frames, 6) or (batch, 6) containing [tx, ty, tz, rx, ry, rz]
    """
    if transforms.dim() == 3:
        # Single transform per batch item
        batch_size = transforms.shape[0]
        # Extract translation
        translation = transforms[:, :3, 3]  # (batch, 3)
        # Extract rotation matrix and convert to euler angles
        rotation_matrices = transforms[:, :3, :3]  # (batch, 3, 3)
        euler_angles = pytorch3d.transforms.matrix_to_euler_angles(rotation_matrices, 'ZYX')  # (batch, 3)
        # Combine translation and rotation
        dof_params = torch.cat([translation, euler_angles], dim=1)  # (batch, 6)
        return dof_params

    elif transforms.dim() == 4:
        # Multiple transforms per batch item
        batch_size, num_frames = transforms.shape[0], transforms.shape[1]
        # Extract translation
        translation = transforms[:, :, :3, 3]  # (batch, num_frames, 3)
        # Extract rotation matrix and convert to euler angles
        rotation_matrices = transforms[:, :, :3, :3]  # (batch, num_frames, 3, 3)
        # Reshape for pytorch3d
        rot_reshaped = rotation_matrices.view(-1, 3, 3)
        euler_angles = pytorch3d.transforms.matrix_to_euler_angles(rot_reshaped, 'ZYX')  # (batch*num_frames, 3)
        euler_angles = euler_angles.view(batch_size, num_frames, 3)  # (batch, num_frames, 3)
        # Combine translation and rotation
        dof_params = torch.cat([translation, euler_angles], dim=2)  # (batch, num_frames, 6)
        return dof_params

    else:
        raise ValueError(f"Unsupported transform tensor dimensions: {transforms.shape}")


def motion_speed_loss(pred_transforms, gt_transforms):
    """
    Calculate motion speed loss (L_speed) as described in equation (11)

    Args:
        pred_transforms: torch.Tensor of shape (batch, num_frames, 4, 4) - predicted transformations
        gt_transforms: torch.Tensor of shape (batch, num_frames, 4, 4) - ground truth transformations

    Returns:
        torch.Tensor: motion speed loss
    """
    # Extract 6 DOF parameters from transformations
    pred_6dof = extract_6dof_from_transforms(pred_transforms)  # (batch, num_frames, 6)
    gt_6dof = extract_6dof_from_transforms(gt_transforms)      # (batch, num_frames, 6)

    # Calculate velocities between consecutive frames
    pred_velocities = pred_6dof[:, 1:] - pred_6dof[:, :-1]    # (batch, num_frames-1, 6)
    gt_velocities = gt_6dof[:, 1:] - gt_6dof[:, :-1]          # (batch, num_frames-1, 6)

    # Calculate L2 norm of velocity differences
    velocity_diff = pred_velocities - gt_velocities           # (batch, num_frames-1, 6)
    speed_loss = torch.mean(torch.sum(velocity_diff ** 2, dim=2))  # Mean over batch and frames

    return speed_loss


def get_correlation_loss_6dof(labels, outputs, dof_based=True):
    """
    Calculate correlation loss for 6 DOF parameters

    Args:
        labels: Ground truth 6 DOF parameters (batch, 6) or (batch, num_frames, 6)
        outputs: Predicted 6 DOF parameters (batch, 6) or (batch, num_frames, 6)
        dof_based: If True, calculate correlation for each DOF separately

    Returns:
        torch.Tensor: correlation loss
    """
    # If inputs are transformation matrices, extract 6 DOF parameters
    if labels.dim() >= 3 and labels.shape[-1] == 4 and labels.shape[-2] == 4:
        labels = extract_6dof_from_transforms(labels)
    if outputs.dim() >= 3 and outputs.shape[-1] == 4 and outputs.shape[-2] == 4:
        outputs = extract_6dof_from_transforms(outputs)

    if dof_based:
        dof_correlations = []
        num_dofs = labels.shape[-1]  # Should be 6

        for dof_id in range(num_dofs):
            if labels.dim() == 2:
                x = outputs[:, dof_id]
                y = labels[:, dof_id]
            else:  # 3D case - flatten temporal dimension
                x = outputs[:, :, dof_id].flatten()
                y = labels[:, :, dof_id].flatten()

            # Calculate correlation for this DOF
            xy = x * y
            mean_xy = torch.mean(xy)
            mean_x = torch.mean(x)
            mean_y = torch.mean(y)
            cov_xy = mean_xy - mean_x * mean_y

            var_x = torch.sum((x - mean_x) ** 2 / x.shape[0])
            var_y = torch.sum((y - mean_y) ** 2 / y.shape[0])

            corr_xy = cov_xy / (torch.sqrt(var_x * var_y) + 1e-8)  # Add small epsilon for stability
            loss = 1 - corr_xy
            dof_correlations.append(loss)

        loss = sum(dof_correlations) / num_dofs
    else:
        # Flatten all dimensions except batch
        x = outputs.flatten()
        y = labels.flatten()

        xy = x * y
        mean_xy = torch.mean(xy)
        mean_x = torch.mean(x)
        mean_y = torch.mean(y)
        cov_xy = mean_xy - mean_x * mean_y

        var_x = torch.sum((x - mean_x) ** 2 / x.shape[0])
        var_y = torch.sum((y - mean_y) ** 2 / y.shape[0])

        corr_xy = cov_xy / (torch.sqrt(var_x * var_y) + 1e-8)
        loss = 1 - corr_xy

    return loss


def dof_MSE_loss(labels, outputs, criterion):
    """
    Calculate MSE loss for 6 DOF parameters

    Args:
        labels: Ground truth 6 DOF parameters or transformation matrices
        outputs: Predicted 6 DOF parameters or transformation matrices
        criterion: MSE loss function

    Returns:
        torch.Tensor: MSE loss
    """
    # If inputs are transformation matrices, extract 6 DOF parameters
    if labels.dim() >= 3 and labels.shape[-1] == 4 and labels.shape[-2] == 4:
        labels = extract_6dof_from_transforms(labels)
    if outputs.dim() >= 3 and outputs.shape[-1] == 4 and outputs.shape[-2] == 4:
        outputs = extract_6dof_from_transforms(outputs)

    loss = criterion(outputs, labels)
    return loss


if __name__ == '__main__':
    x = np.linspace(1, 50, num=50)
    y = 3 * x
    print(x)
    print(y)
    # loss = correlation_loss(output=x, target=y)
    # print('loss = {:.4f}'.format(loss))
